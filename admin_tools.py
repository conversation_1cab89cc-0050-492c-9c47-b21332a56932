#!/usr/bin/env python3
"""
Admin tools for managing user subscriptions in Platyfend Bot
"""

import sys
import os
from datetime import datetime, timedelta
from bot.db.user_access import UserAccessDB

def show_help():
    print("""
Platyfend Bot Admin Tools
========================

Usage: python admin_tools.py <command> [arguments]

Commands:
  list-users                    - List all users and their subscription status
  user-info <username>          - Show detailed info for a specific user
  set-paid <username> [type] [days] - Set user as paid (default: premium, 30 days)
  set-free <username>           - Set user back to free tier
  extend <username> <days>      - Extend subscription by X days
  check-expired                 - List users with expired subscriptions

Examples:
  python admin_tools.py list-users
  python admin_tools.py user-info john_doe
  python admin_tools.py set-paid john_doe premium 90
  python admin_tools.py set-free john_doe
  python admin_tools.py extend john_doe 30
  python admin_tools.py check-expired
""")

def list_users():
    """List all users and their subscription status"""
    db = UserAccessDB()
    
    # Get all users from database
    query = "SELECT username, is_active, is_paid, subscription_type, subscription_expires_at FROM users"
    cur = db.conn.execute(query)
    users = cur.fetchall()
    
    if not users:
        print("No users found in database.")
        return
    
    print(f"{'Username':<20} {'Active':<8} {'Paid':<6} {'Type':<12} {'Expires':<20}")
    print("-" * 70)
    
    for user in users:
        username = user["username"]
        is_active = "Yes" if user["is_active"] else "No"
        is_paid = "Yes" if user["is_paid"] else "No"
        sub_type = user["subscription_type"] or "free"
        expires = user["subscription_expires_at"] or "Never"
        
        print(f"{username:<20} {is_active:<8} {is_paid:<6} {sub_type:<12} {expires:<20}")

def user_info(username):
    """Show detailed info for a specific user"""
    db = UserAccessDB()
    info = db.get_user_subscription_info(username)
    
    if not info:
        print(f"User '{username}' not found or inactive.")
        return
    
    print(f"User Information for: {username}")
    print("=" * 40)
    print(f"Active: {'Yes' if db.is_authorized(username) else 'No'}")
    print(f"Paid User: {'Yes' if info['is_paid'] else 'No'}")
    print(f"Subscription Type: {info['subscription_type']}")
    print(f"Expires At: {info['subscription_expires_at'] or 'Never'}")
    print(f"Created At: {info['created_at']}")
    print(f"Currently Valid: {'Yes' if db.is_paid_user(username) else 'No'}")

def set_paid(username, subscription_type="premium", days=30):
    """Set user as paid with expiration"""
    db = UserAccessDB()
    
    # Calculate expiration date
    expires_at = (datetime.now() + timedelta(days=int(days))).isoformat()
    
    # Ensure username is lowercase for consistency
    username = username
    # First ensure the user exists and is active
    if not db.is_authorized(username):
        # Add or activate the user first
        db.add_or_update_user(username)
        print(f"Created or activated user '{username}'")
    
    # Set paid subscription
    db.set_paid_subscription(username, subscription_type, expires_at)
    print(f"✅ User '{username}' set to paid ({subscription_type}) until {expires_at}")
    
    # Verify the change
    info = db.get_user_subscription_info(username)
    if info and info['is_paid']:
        print(f"✓ Verified: User is now paid with type '{info['subscription_type']}' until {info['subscription_expires_at']}")
    else:
        print(f"⚠️ Warning: Failed to verify paid status for '{username}'")

def set_free(username):
    """Set user back to free tier"""
    db = UserAccessDB()
    db.revoke_paid_subscription(username)
    print(f"✅ User '{username}' set to free tier")

def extend_subscription(username, days):
    """Extend user's subscription by X days"""
    db = UserAccessDB()
    info = db.get_user_subscription_info(username)
    
    if not info:
        print(f"User '{username}' not found.")
        return
    
    if not info['is_paid']:
        print(f"User '{username}' is not currently paid. Use 'set-paid' instead.")
        return
    
    # Calculate new expiration date
    current_expires = info['subscription_expires_at']
    if current_expires:
        try:
            current_date = datetime.fromisoformat(current_expires)
        except ValueError:
            current_date = datetime.now()
    else:
        current_date = datetime.now()
    
    new_expires = (current_date + timedelta(days=int(days))).isoformat()
    
    db.update_subscription(username, True, info['subscription_type'], new_expires)
    print(f"✅ Extended '{username}' subscription by {days} days until {new_expires}")

def check_expired():
    """List users with expired subscriptions"""
    db = UserAccessDB()
    
    query = """
    SELECT username, subscription_expires_at, subscription_type 
    FROM users 
    WHERE is_paid = 1 AND subscription_expires_at IS NOT NULL
    """
    cur = db.conn.execute(query)
    users = cur.fetchall()
    
    now = datetime.now()
    expired_users = []
    
    for user in users:
        try:
            expires_at = datetime.fromisoformat(user["subscription_expires_at"])
            if expires_at < now:
                expired_users.append(user)
        except ValueError:
            continue
    
    if not expired_users:
        print("No expired subscriptions found.")
        return
    
    print("Expired Subscriptions:")
    print("=" * 40)
    for user in expired_users:
        print(f"Username: {user['username']}")
        print(f"Type: {user['subscription_type']}")
        print(f"Expired: {user['subscription_expires_at']}")
        print("-" * 20)

def main():
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1]
    
    if command == "list-users":
        list_users()
    elif command == "user-info":
        if len(sys.argv) < 3:
            print("Error: Username required")
            return
        user_info(sys.argv[2])
    elif command == "set-paid":
        if len(sys.argv) < 3:
            print("Error: Username required")
            return
        username = sys.argv[2]
        sub_type = sys.argv[3] if len(sys.argv) > 3 else "premium"
        days = sys.argv[4] if len(sys.argv) > 4 else 30
        set_paid(username, sub_type, days)
    elif command == "set-free":
        if len(sys.argv) < 3:
            print("Error: Username required")
            return
        set_free(sys.argv[2])
    elif command == "extend":
        if len(sys.argv) < 4:
            print("Error: Username and days required")
            return
        extend_subscription(sys.argv[2], sys.argv[3])
    elif command == "check-expired":
        check_expired()
    else:
        print(f"Unknown command: {command}")
        show_help()

if __name__ == "__main__":
    main()

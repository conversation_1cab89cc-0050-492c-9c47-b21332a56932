"""
Simple FastAPI application for Platyfend PR analysis.
"""

from fastapi import Fast<PERSON>I
from fastapi.middleware.cors import CORSMiddleware

from .routes import pr_routes

# Create FastAPI app
app = FastAPI(
    title="Platyfend API",
    description="Security analysis for Pull Requests",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routes
app.include_router(pr_routes.router, prefix="/api/v1")

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "platyfend-api"}
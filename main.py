import aiohttp, sys, os, logging
from aiohttp import web
from gidgethub import aiohttp as gh_aiohttp, routing, sansio
from utils.config import config
from services.auth_service import JWTAuth
from services.github_service import GitHubService
from services.openai_service import OpenAIService
from core.integrations.github import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommentHandler, InstallHandler
from api.oauth import gitlab_oauth_redirect, gitlab_oauth_callback
from core.integrations.gitlab import GitLabWebhook
from api.subscription import SubscriptionHandler
from database.repositories.user_repository import UserRepository
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

logger = logging.getLogger(__name__)

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Initialize subscription handler
subscription_handler = SubscriptionHandler()


# Event handlers are now in api/github.py


async def webhook(request):
    """Handle GitHub webhook requests"""
    from api.github import github_webhook

    return await github_webhook(request)


async def gitlab_webhook(request):
    """Handle GitLab webhook requests"""
    from api.gitlab import gitlab_webhook as gitlab_webhook_handler

    return await gitlab_webhook_handler(request)


if __name__ == "__main__":
    app = web.Application()
    app.router.add_post("/webhook", webhook)
    app.router.add_post("/gitlab", gitlab_webhook)

    app.router.add_get("/oauth/start", gitlab_oauth_redirect)
    app.router.add_get("/oauth/callback", gitlab_oauth_callback)

    # app.router.add_get("/subscription/check", subscription_handler.check_subscription)
    # app.router.add_get("/subscription/status", subscription_handler.subscription_status_page)

    logger.info(f"🚀 Bot running on https://{config.host}:{config.port}/")

    web.run_app(app, port=config.port)


async def webhook(request):
    """Handle GitHub webhook requests"""
    from api.github import github_webhook

    return await github_webhook(request)


async def gitlab_webhook(request):
    """Handle GitLab webhook requests"""
    from api.gitlab import gitlab_webhook as gitlab_webhook_handler

    return await gitlab_webhook_handler(request)


if __name__ == "__main__":
    app = web.Application()
    app.router.add_post("/webhook", webhook)
    app.router.add_post("/gitlab", gitlab_webhook)

    app.router.add_get("/oauth/start", gitlab_oauth_redirect)
    app.router.add_get("/oauth/callback", gitlab_oauth_callback)

    # app.router.add_get("/subscription/check", subscription_handler.check_subscription)
    # app.router.add_get("/subscription/status", subscription_handler.subscription_status_page)

    logger.info(f"🚀 Bot running on https://{config.host}:{config.port}/")

    web.run_app(app, port=config.port)

# Platyfend Bot - Subscription Management Guide

This guide explains how to manage paid users and subscription features in Platyfend Bot.

## Overview

The bot now supports two subscription tiers:

### 🆓 Free Tier
- Limited MR reviews (2 files maximum)
- Basic push notifications (1 file maximum)
- No access to "full review" command
- Standard support

### 💎 Premium Tier
- Unlimited file reviews
- Full access to "full review" command
- Comprehensive push reviews
- Priority support
- Advanced AI analysis

## Database Schema

The user database now includes these subscription-related fields:

- `is_paid` (INTEGER): 1 for paid users, 0 for free
- `subscription_type` (TEXT): "free", "premium", etc.
- `subscription_expires_at` (TEXT): ISO format expiration date
- `created_at` (TEXT): Account creation timestamp
- `updated_at` (TEXT): Last modification timestamp

## Admin Tools

### Setting Up the Database

1. **Migrate existing database:**
   ```bash
   python3 migrate_database.py
   ```

2. **Check migration status:**
   ```bash
   python3 admin_tools.py list-users
   ```

### Managing Users

**List all users:**
```bash
python3 admin_tools.py list-users
```

**Check specific user:**
```bash
python3 admin_tools.py user-info john_doe
```

**Set user as paid (30 days):**
```bash
python3 admin_tools.py set-paid john_doe premium 30
```

**Set user as paid (90 days):**
```bash
python3 admin_tools.py set-paid john_doe premium 90
```

**Extend subscription:**
```bash
python3 admin_tools.py extend john_doe 30
```

**Revoke paid access:**
```bash
python3 admin_tools.py set-free john_doe
```

**Check expired subscriptions:**
```bash
python3 admin_tools.py check-expired
```

## Web Interface

### Subscription Status Page

Users can check their subscription status at:
```
http://your-domain.com/subscription/status
```

### API Endpoint

Check subscription programmatically:
```
GET /subscription/check?username=john_doe
```

Response:
```json
{
  "username": "john_doe",
  "is_authorized": true,
  "is_paid": true,
  "subscription_type": "premium",
  "subscription_expires_at": "2024-08-01T12:00:00",
  "is_subscription_valid": true,
  "created_at": "2024-07-01T10:00:00"
}
```

## Feature Differences

### Merge Request Reviews

**Free Users:**
- Reviews limited to first 2 files
- Shows upgrade message for additional files
- Basic AI analysis

**Paid Users:**
- Reviews all files in MR
- Comprehensive AI analysis
- No limitations

### Comment-Triggered Reviews

**Free Users:**
- "full review" command blocked
- Shows upgrade message instead
- Directed to subscription page

**Paid Users:**
- Full access to "full review" command
- Comprehensive analysis of all files
- Priority processing

### Push Reviews

**Free Users:**
- Limited to 1 file per push
- Basic analysis
- Upgrade prompts for more files

**Paid Users:**
- Reviews all changed files
- Comprehensive analysis
- No limitations

## Integration Examples

### Checking User Status in Code

```python
from bot.db.user_access import UserAccessDB

db = UserAccessDB()

# Check if user is paid
if db.is_paid_user("username"):
    # Provide premium features
    pass
else:
    # Provide limited features
    pass

# Get detailed subscription info
info = db.get_user_subscription_info("username")
print(f"User: {info['username']}")
print(f"Type: {info['subscription_type']}")
print(f"Expires: {info['subscription_expires_at']}")
```

### Setting Subscription Programmatically

```python
from datetime import datetime, timedelta

# Set 30-day premium subscription
expires = (datetime.now() + timedelta(days=30)).isoformat()
db.set_paid_subscription("username", "premium", expires)

# Extend existing subscription
db.extend_subscription("username", 30)

# Revoke subscription
db.revoke_paid_subscription("username")
```

## Monitoring and Maintenance

### Regular Tasks

1. **Check for expired subscriptions:**
   ```bash
   python3 admin_tools.py check-expired
   ```

2. **Review user activity:**
   ```bash
   python3 admin_tools.py list-users
   ```

3. **Monitor logs for subscription-related events:**
   ```bash
   grep "subscription\|paid\|premium" bot.log
   ```

### Automated Cleanup

Consider setting up a cron job to automatically handle expired subscriptions:

```bash
# Daily check for expired subscriptions at 2 AM
0 2 * * * cd /path/to/platyfend-bot && python3 admin_tools.py check-expired
```

## Customization

### Adding New Subscription Types

1. Update the database schema if needed
2. Modify the admin tools to support new types
3. Update the feature logic in handlers
4. Update the web interface

### Changing Feature Limits

Edit the handler files to adjust limits:

- `bot/handlers/gitlab_handler.py` - MR and push review limits
- `bot/handlers/subscription_handler.py` - Web interface text

### Custom Upgrade Messages

Update the upgrade messages in the handler files to point to your payment system or contact information.

## Troubleshooting

### Common Issues

1. **Database migration fails:**
   - Check file permissions
   - Ensure database isn't locked
   - Backup database before migration

2. **User status not updating:**
   - Check database connection
   - Verify admin tool syntax
   - Check logs for errors

3. **Web interface not working:**
   - Ensure routes are properly registered
   - Check server logs
   - Verify subscription handler import

### Debug Commands

```bash
# Check database structure
sqlite3 users.db ".schema users"

# View all users
sqlite3 users.db "SELECT * FROM users;"

# Check specific user
sqlite3 users.db "SELECT * FROM users WHERE username='john_doe';"
```

## Security Considerations

1. **Database Access:** Ensure only authorized personnel can run admin tools
2. **API Endpoints:** Consider adding authentication to subscription endpoints
3. **Logs:** Avoid logging sensitive subscription information
4. **Backups:** Regularly backup the user database

## Support

For issues with subscription management:

1. Check the logs for error messages
2. Verify database integrity
3. Test with admin tools
4. Review this guide for proper usage

Remember to always backup your database before making bulk changes to user subscriptions!

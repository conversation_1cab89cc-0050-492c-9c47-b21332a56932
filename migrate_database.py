#!/usr/bin/env python3
"""
Database migration script for Platyfend Bot
Adds new subscription-related columns to existing users table
"""

import sqlite3
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """Add new columns to existing users table"""
    db_path = os.path.join(os.path.dirname(__file__), "users.db")
    
    if not os.path.exists(db_path):
        logger.info("No existing database found. New database will be created with full schema.")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if new columns already exist
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        new_columns = [
            ("is_paid", "INTEGER DEFAULT 0"),
            ("subscription_type", "TEXT DEFAULT 'free'"),
            ("subscription_expires_at", "TEXT"),
            ("created_at", "TEXT DEFAULT CURRENT_TIMESTAMP"),
            ("updated_at", "TEXT DEFAULT CURRENT_TIMESTAMP")
        ]
        
        for column_name, column_def in new_columns:
            if column_name not in columns:
                logger.info(f"Adding column: {column_name}")
                cursor.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_def}")
            else:
                logger.info(f"Column {column_name} already exists, skipping")
        
        # Update existing users to have proper timestamps if they don't have them
        cursor.execute("""
            UPDATE users 
            SET created_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
            WHERE created_at IS NULL OR created_at = ''
        """)
        
        conn.commit()
        logger.info("✅ Database migration completed successfully!")
        
        # Show current user count
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        logger.info(f"Total users in database: {user_count}")
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()

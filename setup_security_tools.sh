#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

error_exit() {
    echo -e "${RED}Error: $1${NC}" >&2
    exit 1
}

success() {
    echo -e "${G<PERSON><PERSON>}$1${NC}"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo "Starting setup for Go, Semgrep, Bandit, ESLint, Flake8-Heaven, and Ruff using pipx..."

# Step 1: Install prerequisites
if ! command_exists curl; then
    sudo apt-get update || error_exit "Failed to update apt"
    sudo apt-get install -y curl || error_exit "Failed to install curl"
fi

if ! command_exists tar; then
    sudo apt-get install -y tar || error_exit "Failed to install tar"
fi

if ! command_exists python3; then
    sudo apt-get install -y python3 || error_exit "Failed to install python3"
fi

# Ensure pipx is installed
if ! command_exists pipx; then
    echo "Installing pipx..."
    python3 -m pip install pipx --break-system-packages || error_exit "Failed to install pipx"
    python3 -m pipx ensurepath || error_exit "Failed to setup pipx path"
    source ~/.bashrc || true
    success "pipx installed"
fi

if ! command_exists node; then
    sudo apt-get install -y nodejs npm || error_exit "Failed to install nodejs and npm"
fi

# Step 2: Install Go
GO_VERSION="1.23.2"
GO_TARBALL="go${GO_VERSION}.linux-amd64.tar.gz"
GO_URL="https://go.dev/dl/${GO_TARBALL}"
GO_INSTALL_DIR="/usr/local/go"

if ! command_exists go || [[ "$(go version 2>/dev/null | awk '{print $3}')" != "go${GO_VERSION}" ]]; then
    echo "Installing Go ${GO_VERSION}..."
    curl -fsSL "${GO_URL}" -O || error_exit "Failed to download Go"
    sudo rm -rf "${GO_INSTALL_DIR}"
    sudo tar -C /usr/local -xzf "${GO_TARBALL}" || error_exit "Failed to extract Go"
    rm "${GO_TARBALL}"
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
    export PATH=$PATH:/usr/local/go/bin
    success "Go ${GO_VERSION} installed"
else
    success "Go $(go version | awk '{print $3}') already installed"
fi

# Step 3: Install Semgrep via pipx
if ! command_exists semgrep; then
    echo "Installing Semgrep with pipx..."
    pipx install semgrep || error_exit "Failed to install Semgrep"
    success "Semgrep installed"
else
    success "Semgrep already installed"
fi

# Step 4: Install Bandit via pipx
if ! command_exists bandit; then
    echo "Installing Bandit with pipx..."
    pipx install bandit || error_exit "Failed to install Bandit"
    success "Bandit installed"
else
    success "Bandit already installed"
fi

# Step 5: Install ESLint globally
if ! command_exists eslint; then
    echo "Installing ESLint and plugins..."
    sudo npm install -g eslint eslint@9.28.0 \
        @typescript-eslint/eslint-plugin @typescript-eslint/parser \
        eslint-plugin-node eslint-plugin-security @next/eslint-plugin-next \
        eslint-plugin-import eslint-plugin-prettier globals prettier || error_exit "Failed to install ESLint and plugins"
    success "ESLint installed"
else
    success "ESLint already installed"
fi

# Step 6: Install Flake8-Heaven via pipx
if ! command_exists flake8; then
    echo "Installing Flake8-Heaven with pipx..."
    pipx install flake8 || error_exit "Failed to install Flake8"
    success "Flake8-Heaven installed"
else
    success "Flake8-Heaven already installed"
fi

# Step 7: Install ast-grep-cli via pipx
if ! command_exists ast-grep; then
    echo "Installing ast-grep-cli with pipx..."
    pipx install ast-grep-cli || error_exit "Failed to install ast-grep-cli"
    success "ast-grep-cli installed"
else
    success "ast-grep-cli already installed"
fi

# Step 8: Install Ruff via pipx
if ! command_exists ruff; then
    echo "Installing Ruff with pipx..."
    pipx install ruff || error_exit "Failed to install Ruff"
    success "Ruff installed"
else
    success "Ruff already installed"
fi

# Step 9: Verify installations
echo "Verifying installs..."
command_exists go && go version || error_exit "Go not found"
command_exists semgrep && semgrep --version || error_exit "Semgrep not found"
command_exists bandit && bandit --version || error_exit "Bandit not found"
command_exists eslint && eslint --version || error_exit "ESLint not found"
command_exists flake8 && flake8 --version || error_exit "Flake8-Heaven not found"
command_exists ast-grep && ast-grep --version || error_exit "Ast-grep not found"
command_exists ruff && ruff --version || error_exit "Ruff not found"

success "Setup complete!"
echo "Restart your terminal or run 'source ~/.bashrc' to update PATH if needed."
